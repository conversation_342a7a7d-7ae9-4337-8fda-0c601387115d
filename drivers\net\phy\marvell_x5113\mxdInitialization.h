/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/********************************************************************************
* mxdInitialization.h
*
* DESCRIPTION:
*       API Prototypes for Marvell X5113 Device for initializing the driver.
*
* DEPENDENCIES:
*
* FILE REVISION NUMBER:
*
*******************************************************************************/

#ifndef MXDINITIALIZATION_H
#define MXDINITIALIZATION_H

#include "mxdApiTypes.h"

#ifdef __cplusplus
extern "C" {
#endif

/*******************************************************************************
 MXD_STATUS mxdInitDriver
 (
    IN FMXD_READ_MDIO  readMdio,
    IN FMXD_WRITE_MDIO writeMdio,
    IN MXD_U16         mdioPort,
    IN MXD_U16         *pZ80Image,
    IN MXD_U16         z80Size,
    IN MXD_U16         *pBusMasterImage,
    IN MXD_U16         busMasterSize,
    IN MXD_U16         *pSerdesImage,
    IN MXD_U16         serdesSize,
    IN MXD_PVOID       pHostContext,
    OUT MXD_DEV_PTR    pDev
 );

 Inputs:
    readMdio - pointer to host's function to read MDIO
    writeMdio - pointer to host's function to write MDIO
    mdioPort - MDIO port address, 0-31
    pZ80Image - pointer to Z80 firmware image
    z80Size - size of Z80 firmware image in words
    pBusMasterImage - pointer to SBus master firmware image
    busMasterSize - size of SBus master firmware image in words
    pSerdesImage - pointer to SerDes firmware image
    serdesSize - size of SerDes firmware image in words
    pHostContext - user specific data for host to pass to the low layer

 Outputs:
    pDev - pointer to MXD_DEV structure to be initialized

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    This function initializes the MXD driver and downloads firmware to the device.
    It must be called before any other API functions.

 Side effects:
    None

 Notes/Warnings:
    Do not run any process to access the mdioPort while the firmware download is in 
    process.

*******************************************************************************/
MXD_STATUS mxdInitDriver(
    FMXD_READ_MDIO  readMdio,
    FMXD_WRITE_MDIO writeMdio,
    MXD_U16         mdioPort,
    MXD_U16         *pZ80Image,
    MXD_U16         z80Size,
    MXD_U16         *pBusMasterImage,
    MXD_U16         busMasterSize,
    MXD_U16         *pSerdesImage,
    MXD_U16         serdesSize,
    MXD_PVOID       pHostContext,
    MXD_DEV_PTR     pDev
);

/*******************************************************************
MXD_STATUS mxdUnloadDriver
(
    IN MXD_DEV_PTR pDev
);

 Inputs:
    pDev - pointer to MXD_DEV initialized by mxdInitDriver() call

 Outputs:
    None

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    This function performs the clean-up when the driver unloads. 
    This function must be called before terminating the process to prevent memory leakage.

 Side effects:
    None

 Notes/Warnings:
    None

*******************************************************************/
MXD_STATUS mxdUnloadDriver(
    MXD_DEV_PTR pDev
);

#ifdef __cplusplus
}
#endif

#endif /* MXDINITIALIZATION_H */
