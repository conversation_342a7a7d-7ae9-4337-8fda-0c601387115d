/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/********************************************************************
This file contains functions prototypes and global defines/data for
higher-level functions using MDIO access to download firmware into 
the internal RAM and programming the EEPROM of the Marvell X5113 PHY.
********************************************************************/
#ifndef MXDFWDOWNLOAD_H
#define MXDFWDOWNLOAD_H

#include "mxdApiTypes.h"

#ifdef __cplusplus
extern "C" {
#endif

/*******************************************************************************
 MXD_STATUS mxdUpdateEEPROMImage
 (
    IN MXD_DEV_PTR pDev,
    IN MXD_U16 mdioPort,
    IN MXD_U16 *pBusMasterImage,
    IN MXD_U16 busMasterSize,
    IN MXD_U16 *pSerdesImage,
    IN MXD_U16 serdesSize,
    IN MXD_U16 *pZ80Image,
    IN MXD_U16 z80Size,
    OUT MXD_U16 *errCode
 );

 Inputs:
    pDev - pointer to MXD_DEV initialized by mxdInitDriver() call
    mdioPort - MDIO port address, 0-31
    pBusMasterImage - pointer to SBus master firmware image
    busMasterSize - size of SBus master firmware image in words
    pSerdesImage - pointer to SerDes firmware image
    serdesSize - size of SerDes firmware image in words
    pZ80Image - pointer to Z80 firmware image
    z80Size - size of Z80 firmware image in words

 Outputs:
    errCode - error code if operation fails

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    This function updates the EEPROM with the provided firmware images.
    The SBus master and SerDes images are required, while the Z80 image is optional.

 Side effects:
    None

 Notes/Warnings:
    Do not run any process to access the mdioPort while the EEPROM update is in 
    process. This operation may take several minutes to complete.

*******************************************************************************/
MXD_STATUS mxdUpdateEEPROMImage(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 *pBusMasterImage,
    MXD_U16 busMasterSize,
    MXD_U16 *pSerdesImage,
    MXD_U16 serdesSize,
    MXD_U16 *pZ80Image,
    MXD_U16 z80Size,
    MXD_U16 *errCode
);

/*******************************************************************************
 MXD_STATUS mxdDownloadSBusMasterFW
 (
    IN MXD_DEV_PTR pDev,
    IN MXD_U16 mdioPort,
    IN MXD_U16 *pSBusMasterImage,
    IN MXD_U16 sbusImageSize
 );

 Inputs:
    pDev - pointer to MXD_DEV initialized by mxdInitDriver() call
    mdioPort - MDIO port address, 0-31
    pSBusMasterImage - pointer to SBus master firmware image
    sbusImageSize - size of SBus master firmware image in words

 Outputs:
    None

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    This function downloads the SBus master firmware image to the chip. The input 
    file image is read in as a string of 10bit hex value. This function will 
    pack the 10bit into 32bit value and load them into the SBus master. After 
    the image load completed, the CRC will be checked for correctness.

 Side effects:
    None

 Notes/Warnings:
    Do not run any process to access the mdioPort while the firmware download is in 
    process.

*******************************************************************************/
MXD_STATUS mxdDownloadSBusMasterFW(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 *pSBusMasterImage,
    MXD_U16 sbusImageSize
);

#ifdef __cplusplus
}
#endif

#endif /* MXDFWDOWNLOAD_H */
