/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/********************************************************************
This file contains common types and defines across the Marvell
X5113 API driver (MXD).
********************************************************************/
#ifndef MXD_TYPES_H
#define MXD_TYPES_H

#ifdef __cplusplus
extern "C" {
#endif

/* Basic type definitions */
typedef unsigned char   MXD_U8;
typedef char            MXD_8;
typedef unsigned short  MXD_U16;
typedef short           MXD_16;
typedef unsigned int    MXD_U32;
typedef int             MXD_32;
typedef void            MXD_VOID;
typedef void*           MXD_PVOID;
typedef unsigned char   MXD_BOOL;

/* Status definitions */
typedef enum {
    MXD_OK = 0,
    MXD_FAIL = 1
} MXD_STATUS;

/* Boolean definitions */
#define MXD_TRUE    1
#define MXD_FALSE   0

/* Interface definitions */
#define MXD_HOST    0
#define MXD_LINE    1
#define MXD_NUM_INTERFACE   2
#define MXD_NUM_LANES       4

/* MDIO function pointer types */
typedef MXD_STATUS (*FMXD_READ_MDIO)(
    void* pDev,
    MXD_U16 mdioPort,
    MXD_U16 mmd,
    MXD_U16 reg,
    MXD_U16* value
);

typedef MXD_STATUS (*FMXD_WRITE_MDIO)(
    void* pDev,
    MXD_U16 mdioPort,
    MXD_U16 mmd,
    MXD_U16 reg,
    MXD_U16 value
);

/* Device structure */
typedef struct _MXD_DEV {
    MXD_U16             mdioPort;       /* MDIO port number */
    MXD_U16             serdesID[MXD_NUM_INTERFACE][MXD_NUM_LANES];
    MXD_BOOL            devEnabled;     /* whether mxdInitDriver() called successfully */
    MXD_U32             devInfo;        /* operations, features, status tracking */
    FMXD_READ_MDIO      fmxdReadMdio;   /* passed in function for MDIO Read  */
    FMXD_WRITE_MDIO     fmxdWriteMdio;  /* passed in function for MDIO Write */
    MXD_PVOID           hostContext;    /* user specific data for host to pass to the low layer */
} MXD_DEV, *MXD_DEV_PTR;

/* Maximum MDIO port number */
#define MXD_MAX_MDIO_NUM    31

/* Debug and test environment settings */
#define DB_TEST_ENVIRONMENT 0
#define MXD_DEBUG           0
#define MXD_ENABLE_SERDES_API 1

/* C linkage settings */
#define C_LINKAGE 1

#ifdef __cplusplus
}
#endif

#endif /* MXD_TYPES_H */
