/*
 * Skeleton device tree; the bare minimum needed to boot; just include and
 * add a compatible value.  The bootloader will typically populate the memory
 * node.
 */

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	chosen { };
	aliases { };

	cpu_card {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		u-boot,dm-pre-reloc;

		timer@0 {
			compatible = "snps,arc-timer";
			clocks = <&core_clk>;
			reg = <0 1>;
		};
	};

	memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x10000000>; /* 256M */
	};
};
