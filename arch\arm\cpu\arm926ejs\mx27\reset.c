// SPDX-License-Identifier: GPL-2.0+
/*
 * (C) Copyright 2002
 * Sysgo Real-Time Solutions, GmbH <www.elinos.com>
 * <PERSON> <<EMAIL>>
 *
 * (C) Copyright 2002
 * Sysgo Real-Time Solutions, GmbH <www.elinos.com>
 * <PERSON> <<EMAIL>>
 *
 * (C) Copyright 2002
 * <PERSON>, DENX Software Engineering, <<EMAIL>>
 *
 * (C) Copyright 2009
 * Ilya <PERSON>, Emcraft Systems Ltd, <<EMAIL>>
 */

#include <common.h>
#include <cpu_func.h>
#include <asm/io.h>
#include <asm/arch/imx-regs.h>

/*
 * Reset the cpu by setting up the watchdog timer and let it time out
 */
void reset_cpu(void)
{
	struct wdog_regs *regs = (struct wdog_regs *)IMX_WDT_BASE;
	/* Disable watchdog and set Time-Out field to 0 */
	writew(0x0000, &regs->wcr);

	/* Write Service Sequence */
	writew(0x5555, &regs->wsr);
	writew(0xAAAA, &regs->wsr);

	/* Enable watchdog */
	writew(WCR_WDE, &regs->wcr);

	while (1);
	/*NOTREACHED*/
}
