/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/********************************************************************
This file contains function prototypes for mode selections, interrupts
and real-time controls, configurations and status for the Marvell
X5113 PHY.
********************************************************************/
#ifndef MXD_API_H
#define MXD_API_H

#include "mxdApiTypes.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MXD_API_MAJOR_VERSION 11
#define MXD_API_MINOR_VERSION 3
#define MXD_API_BUILD_ID 0

/*******************************************************************************
 MXD_VOID mxdGetAPIVersion
 (
    OUT MXD_U8 *major,
    OUT MXD_U8 *minor,
    OUT MXD_U8 *buildID
 );

 Inputs:
    None

 Outputs:
    major - major version number
    minor - minor version number
    buildID - build ID for the API

 Returns:
    None
    
 Description:
    Returns the version and build ID numbers of this API. The non-zero build ID 
    is used for test and preview releases.  General release build ID is 0.

 Side effects:
    None

 Notes/Warnings:
    None

******************************************************************************/
MXD_VOID mxdGetAPIVersion(
    MXD_U8 *major,
    MXD_U8 *minor,
    MXD_U8 *buildID
);

/*******************************************************************************
 MXD_STATUS mxdLanePowerup
 (
    IN MXD_DEV_PTR pDev,
    IN MXD_U16 mdioPort,
    IN MXD_U16 host_or_line,
    IN MXD_U16 laneOffset
 );

 Inputs:
    pDev - pointer to MXD_DEV initialized by mxdInitDriver() call
    mdioPort - MDIO port address, 0-31
    host_or_line - which interface is being modified:
                   MXD_HOST_SIDE
                   MXD_LINE_SIDE
    laneOffset - lane number 0-3

 Outputs:
    None

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    Powers up the specified lane.

 Side effects:
    None

 Notes/Warnings:
    None

******************************************************************************/
MXD_STATUS mxdLanePowerup(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 host_or_line,
    MXD_U16 laneOffset
);

/*******************************************************************************
 MXD_STATUS mxdLanePowerdown
 (
    IN MXD_DEV_PTR pDev,
    IN MXD_U16 mdioPort,
    IN MXD_U16 host_or_line,
    IN MXD_U16 laneOffset
 );

 Inputs:
    pDev - pointer to MXD_DEV initialized by mxdInitDriver() call
    mdioPort - MDIO port address, 0-31
    host_or_line - which interface is being modified:
                   MXD_HOST_SIDE
                   MXD_LINE_SIDE
    laneOffset - lane number 0-3

 Outputs:
    None

 Returns:
    MXD_OK if successful, MXD_FAIL if not

 Description:
    Powers down the specified lane.

 Side effects:
    None

 Notes/Warnings:
    None

******************************************************************************/
MXD_STATUS mxdLanePowerdown(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 host_or_line,
    MXD_U16 laneOffset
);

#ifdef __cplusplus
}
#endif

#endif /* MXD_API_H */
