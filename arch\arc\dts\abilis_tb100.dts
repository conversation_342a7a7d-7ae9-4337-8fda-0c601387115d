// SPDX-License-Identifier: GPL-2.0+
/*
 * Copyright (C) 2015 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

#include "skeleton.dtsi"

/ {
	model = "abilis,tb100";

	aliases {
		console = &uart0;
	};

	cpu_card {
		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <500000000>;
			u-boot,dm-pre-reloc;
		};
	};

	uart0: serial@ff100000 {
			compatible = "snps,dw-apb-uart";
			reg = <0xff100000 0x1000>;
			reg-shift = <2>;
			reg-io-width = <4>;
	};
};
