/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/**********************************************************************
This file contains function prototypes for mode selections, interrupts
and real-time controls, configurations and status for the Marvell
X5113 PHY.
**********************************************************************/
#include "mxdApiTypes.h"
#include "mxdApi.h"

MXD_VOID mxdGetAPIVersion(
    MXD_U8 *major,
    MXD_U8 *minor,
    MXD_U8 *buildID
)
{
    if (major)
        *major = MXD_API_MAJOR_VERSION;
    if (minor)
        *minor = MXD_API_MINOR_VERSION;
    if (buildID)
        *buildID = MXD_API_BUILD_ID;
}

MXD_STATUS mxdLanePowerup(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 host_or_line,
    MXD_U16 laneOffset
)
{
    /* Stub implementation - would need actual hardware-specific code */
    if (!pDev || !pDev->devEnabled)
        return MXD_FAIL;
    
    if (mdioPort > MXD_MAX_MDIO_NUM || laneOffset >= MXD_NUM_LANES)
        return MXD_FAIL;
    
    /* TODO: Implement actual lane power up sequence */
    return MXD_OK;
}

MXD_STATUS mxdLanePowerdown(
    MXD_DEV_PTR pDev,
    MXD_U16 mdioPort,
    MXD_U16 host_or_line,
    MXD_U16 laneOffset
)
{
    /* Stub implementation - would need actual hardware-specific code */
    if (!pDev || !pDev->devEnabled)
        return MXD_FAIL;
    
    if (mdioPort > MXD_MAX_MDIO_NUM || laneOffset >= MXD_NUM_LANES)
        return MXD_FAIL;
    
    /* TODO: Implement actual lane power down sequence */
    return MXD_OK;
}
