/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/***********************************************************************
This file contains sample functions code for calling the driver initialization
and loading firmware/ROM images to the Marvell X5113 PHY.

For Reference Only.



Copyright 2025 Zhao Zheng <<EMAIL>>

***********************************************************************/

// U-Boot driver for Marvell X5113 PHY with firmware loading

#include <common.h>
#include <phy.h>
#include <dm.h>
#include <linux/bitops.h>
#include <linux/delay.h>
#include <fs.h>

#include "mxdApiTypes.h"
#include "mxdApi.h"
#include "mxdInitialization.h"
#include "mxdFwDownload.h"

/* Global device structure */
static MXD_DEV mxd_dev;
static int mxd_initialized = 0;

/* Firmware file paths */
#define MXD_Z80_IMAGE      "marvell/z80_firmware.txt"
#define MXD_SBUS_IMAGE     "marvell/sbus_master.0x101C_1064_0245.rom""
#define MXD_SERDES_IMAGE   "marvell/serdes.0x1064_0245.rom"

/* MDIO read function for MXD API */
static MXD_STATUS mxd_read_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                               MXD_U16 mmd, MXD_U16 reg, MXD_U16* value)
{
    struct phy_device *phydev = pDev->hostContext;
    int ret;

    ret = phy_read_mmd(phydev, mmd, reg);
    if (ret < 0)
        return MXD_FAIL;

    *value = (MXD_U16)ret;
    return MXD_OK;
}

/* MDIO write function for MXD API */
static MXD_STATUS mxd_write_mdio(MXD_DEV_PTR pDev, MXD_U16 mdioPort,
                                MXD_U16 mmd, MXD_U16 reg, MXD_U16 value)
{
    struct phy_device *phydev = pDev->hostContext;
    int ret;

    ret = phy_write_mmd(phydev, mmd, reg, value);
    if (ret < 0)
        return MXD_FAIL;

    return MXD_OK;
}

/* Load firmware image from file */
static MXD_STATUS mxd_load_image_file(const char *filename, MXD_U16 *rom_size, MXD_U16 **rom_ptr)
{
    loff_t file_size;
    char *buffer;
    int ret, i, addr = 0;
    MXD_U16 *rom;

    /* Check if file exists */
    ret = fs_size(filename, &file_size);
    if (ret < 0) {
        printf("File %s not found\n", filename);
        return MXD_FAIL;
    }

    /* Allocate buffer for file content */
    buffer = malloc(file_size + 1);
    if (!buffer) {
        printf("Failed to allocate memory for %s\n", filename);
        return MXD_FAIL;
    }

    /* Read file content */
    ret = fs_read(filename, (ulong)buffer, 0, file_size, NULL);
    if (ret < 0) {
        printf("Failed to read %s\n", filename);
        free(buffer);
        return MXD_FAIL;
    }
    buffer[file_size] = '\0';

    /* Calculate number of words in the file */
    *rom_size = file_size / 4;  /* Each word is 4 bytes in hex format */

    /* Allocate memory for ROM image */
    rom = malloc(sizeof(MXD_U16) * (*rom_size + 2));
    if (!rom) {
        printf("Failed to allocate memory for ROM image\n");
        free(buffer);
        return MXD_FAIL;
    }

    /* Parse file content */
    for (i = 0; i < file_size; i += 4) {
        char hex[5] = {0};
        strncpy(hex, &buffer[i], 4);
        rom[addr++] = (MXD_U16)simple_strtoul(hex, NULL, 16);
    }

    *rom_size = addr;
    rom[*rom_size] = 0;
    rom[*rom_size + 1] = 0;

    free(buffer);
    *rom_ptr = rom;
    return MXD_OK;
}

/* Initialize the X5113 PHY with firmware loading */
static int marvell_x5113_init(struct phy_device *phydev)
{
    MXD_STATUS status;
    MXD_U16 *z80_image = NULL, *sbus_image = NULL, *serdes_image = NULL;
    MXD_U16 z80_size = 0, sbus_size = 0, serdes_size = 0;
    MXD_U16 err_code = 0;

    /* Skip if already initialized */
    if (mxd_initialized)
        return 0;

    printf("Initializing Marvell X5113 PHY with firmware loading...\n");

    /* Load firmware images */
    if (mxd_load_image_file(MXD_Z80_IMAGE, &z80_size, &z80_image) != MXD_OK) {
        printf("Warning: Z80 firmware not loaded, continuing without it\n");
        z80_image = NULL;
        z80_size = 0;
    }

    if (mxd_load_image_file(MXD_SBUS_IMAGE, &sbus_size, &sbus_image) != MXD_OK) {
        printf("Error: Failed to load SBus Master firmware\n");
        goto cleanup;
    }

    if (mxd_load_image_file(MXD_SERDES_IMAGE, &serdes_size, &serdes_image) != MXD_OK) {
        printf("Error: Failed to load SerDes firmware\n");
        goto cleanup;
    }

    /* Initialize the MXD driver with firmware */
    status = mxdInitDriver(mxd_read_mdio, mxd_write_mdio,
                          phydev->addr, /* mdioPort */
                          z80_image, z80_size,
                          sbus_image, sbus_size,
                          serdes_image, serdes_size,
                          phydev,       /* hostContext */
                          &mxd_dev);

    if (status != MXD_OK) {
        printf("Failed to initialize X5113 PHY driver\n");
        goto cleanup;
    }

    mxd_initialized = 1;
    printf("X5113 PHY initialized successfully\n");

cleanup:
    /* Free allocated memory */
    if (z80_image)
        free(z80_image);
    if (sbus_image)
        free(sbus_image);
    if (serdes_image)
        free(serdes_image);

    return (mxd_initialized) ? 0 : -1;
}

/* Update EEPROM with firmware images */
static int marvell_x5113_update_eeprom(struct phy_device *phydev)
{
    MXD_STATUS status;
    MXD_U16 *z80_image = NULL, *sbus_image = NULL, *serdes_image = NULL;
    MXD_U16 z80_size = 0, sbus_size = 0, serdes_size = 0;
    MXD_U16 err_code = 0;

    printf("Updating X5113 PHY EEPROM with firmware...\n");

    /* Load firmware images */
    if (mxd_load_image_file(MXD_Z80_IMAGE, &z80_size, &z80_image) != MXD_OK) {
        printf("Warning: Z80 firmware not loaded\n");
        z80_image = NULL;
        z80_size = 0;
    }

    if (mxd_load_image_file(MXD_SBUS_IMAGE, &sbus_size, &sbus_image) != MXD_OK) {
        printf("Error: Failed to load SBus Master firmware\n");
        goto cleanup;
    }

    if (mxd_load_image_file(MXD_SERDES_IMAGE, &serdes_size, &serdes_image) != MXD_OK) {
        printf("Error: Failed to load SerDes firmware\n");
        goto cleanup;
    }

    /* Update EEPROM with firmware images */
    status = mxdUpdateEEPROMImage(&mxd_dev, phydev->addr,
                                 sbus_image, sbus_size,
                                 serdes_image, serdes_size,
                                 z80_image, z80_size,
                                 &err_code);

    if (status != MXD_OK) {
        printf("Failed to update X5113 PHY EEPROM (error code: 0x%x)\n", err_code);
        goto cleanup;
    }

    printf("X5113 PHY EEPROM updated successfully\n");

cleanup:
    /* Free allocated memory */
    if (z80_image)
        free(z80_image);
    if (sbus_image)
        free(sbus_image);
    if (serdes_image)
        free(serdes_image);

    return (status == MXD_OK) ? 0 : -1;
}

/* Configure the X5113 PHY */
static int marvell_x5113_config(struct phy_device *phydev)
{
    MXD_STATUS status;

    /* Make sure PHY is initialized */
    if (!mxd_initialized) {
        status = marvell_x5113_init(phydev);
        if (status != 0)
            return status;
    }

    /* Power up all lanes */
    status = mxdLanePowerup(&mxd_dev, phydev->addr, MXD_HOST, 0);
    if (status != MXD_OK) {
        printf("Failed to power up X5113 PHY lanes\n");
        return -1;
    }

    /* Set the desired mode based on phydev->speed */
    switch (phydev->speed) {
    case SPEED_10000:
        /* Configure for 10G operation */
        break;
    case SPEED_1000:
        /* Configure for 1G operation */
        break;
    default:
        printf("Unsupported speed %d\n", phydev->speed);
        return -1;
    }

    return 0;
}

/* Shutdown the X5113 PHY */
static int marvell_x5113_shutdown(struct phy_device *phydev)
{
    if (mxd_initialized) {
        /* Power down all lanes */
        mxdLanePowerdown(&mxd_dev, phydev->addr, MXD_HOST, 0);

        /* Unload the driver */
        mxdUnloadDriver(&mxd_dev);
        mxd_initialized = 0;
    }

    return 0;
}

static struct phy_driver marvell_x5113_driver = {
    .name = "Marvell X5113",
    .uid = 0x01410e30,    /* Update with actual PHY ID */
    .mask = 0xfffffff0,
    .features = PHY_GBIT_FEATURES,
    .config = marvell_x5113_config,
    .startup = genphy_startup,
    .shutdown = marvell_x5113_shutdown,
};

int phy_marvell_x5113_init(void)
{
    phy_register(&marvell_x5113_driver);
    return 0;
}

/* Command to update X5113 PHY EEPROM */
static int do_marvell_x5113_update(cmd_tbl_t *cmdtp, int flag, int argc, char * const argv[])
{
    struct phy_device *phydev;
    int ret;

    /* Find the X5113 PHY */
    phydev = phy_find_by_mask(NULL, 0xffffffff, marvell_x5113_driver.uid & marvell_x5113_driver.mask);
    if (!phydev) {
        printf("X5113 PHY not found\n");
        return CMD_RET_FAILURE;
    }

    /* Initialize if not already done */
    if (!mxd_initialized) {
        ret = marvell_x5113_init(phydev);
        if (ret != 0) {
            printf("Failed to initialize X5113 PHY\n");
            return CMD_RET_FAILURE;
        }
    }

    /* Update EEPROM */
    ret = marvell_x5113_update_eeprom(phydev);
    if (ret != 0) {
        printf("Failed to update X5113 PHY EEPROM\n");
        return CMD_RET_FAILURE;
    }

    return CMD_RET_SUCCESS;
}

U_BOOT_CMD(
    x5113_update, 1, 0, do_marvell_x5113_update,
    "Update Marvell X5113 PHY EEPROM with firmware",
    ""
);
