// SPDX-License-Identifier: GPL-2.0+
/*
 * Copyright (C) 2017-2020 Synopsys, Inc. All rights reserved.
 * Author: <PERSON><PERSON><PERSON><PERSON> <Eugen<PERSON><PERSON>.<PERSON><PERSON>@synopsys.com>
 */
/dts-v1/;

#include "skeleton.dtsi"
#include "dt-bindings/clock/snps,hsdk-cgu.h"
#include "dt-bindings/reset/snps,hsdk-reset.h"

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		console = &uart0;
		spi0 = &spi0;
	};

	cpu_card {
		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <500000000>;
			u-boot,dm-pre-reloc;
		};
	};

	clk-fmeas {
		clocks = <&cgu_clk CLK_ARC_PLL>, <&cgu_clk CLK_SYS_PLL>,
			 <&cgu_clk CLK_TUN_PLL>, <&cgu_clk CLK_DDR_PLL>,
			 <&cgu_clk CLK_ARC>, <&cgu_clk CLK_HDMI_PLL>,
			 <&cgu_clk CLK_TUN_TUN>, <&cgu_clk CLK_HDMI>,
			 <&cgu_clk CLK_SYS_APB>, <&cgu_clk CLK_SYS_AXI>,
			 <&cgu_clk CLK_SYS_ETH>, <&cgu_clk CLK_SYS_USB>,
			 <&cgu_clk CLK_SYS_SDIO>, <&cgu_clk CLK_SYS_HDMI>,
			 <&cgu_clk CLK_SYS_GFX_CORE>, <&cgu_clk CLK_SYS_GFX_DMA>,
			 <&cgu_clk CLK_SYS_GFX_CFG>, <&cgu_clk CLK_SYS_DMAC_CORE>,
			 <&cgu_clk CLK_SYS_DMAC_CFG>, <&cgu_clk CLK_SYS_SDIO_REF>,
			 <&cgu_clk CLK_SYS_SPI_REF>, <&cgu_clk CLK_SYS_I2C_REF>,
			 <&cgu_clk CLK_SYS_UART_REF>, <&cgu_clk CLK_SYS_EBI_REF>,
			 <&cgu_clk CLK_TUN_ROM>, <&cgu_clk CLK_TUN_PWM>,
			 <&cgu_clk CLK_TUN_TIMER>;
		clock-names = "cpu-pll", "sys-pll",
			      "tun-pll", "ddr-clk",
			      "cpu-clk", "hdmi-pll",
			      "tun-clk", "hdmi-clk",
			      "apb-clk", "axi-clk",
			      "eth-clk", "usb-clk",
			      "sdio-clk", "hdmi-sys-clk",
			      "gfx-core-clk", "gfx-dma-clk",
			      "gfx-cfg-clk", "dmac-core-clk",
			      "dmac-cfg-clk", "sdio-ref-clk",
			      "spi-clk", "i2c-clk",
			      "uart-clk", "ebi-clk",
			      "rom-clk", "pwm-clk",
			      "timer-clk";
	};

	cgu_clk: cgu-clk@f0000000 {
		compatible = "snps,hsdk-cgu-clock";
		reg = <0xf0000000 0x10>, <0xf00014B8 0x4>;
		#clock-cells = <1>;
	};

	cgu_rst: reset-controller@f00008a0 {
		compatible = "snps,hsdk-reset";
		#reset-cells = <1>;
		reg = <0xf00008a0 0x4>, <0xf0000ff0 0x4>;
	};

	uart0: serial0@f0005000 {
		compatible = "snps,dw-apb-uart";
		reg = <0xf0005000 0x1000>;
		reg-shift = <2>;
		reg-io-width = <4>;
	};

	ethernet@f0008000 {
		#interrupt-cells = <1>;
		compatible = "snps,arc-dwmac-3.70a";
		reg = <0xf0008000 0x2000>;
		phy-mode = "gmii";
	};

	ehci@f0040000 {
		compatible = "generic-ehci";
		reg = <0xf0040000 0x100>;

		/*
		 * OHCI and EHCI have reset line shared so we don't add
		 * reset property to OHCI node as it is probed later and
		 * it will reset sucessfuly probed and configured EHCI HW.
		 */
		resets = <&cgu_rst HSDK_USB_RESET>;
	};

	ohci@f0060000 {
		compatible = "generic-ohci";
		reg = <0xf0060000 0x100>;
	};

	mmcclk_ciu: mmcclk-ciu {
		compatible = "fixed-clock";
		/*
		 * DW sdio controller has external ciu clock divider
		 * controlled via register in SDIO IP. Due to its
		 * unexpected default value (it should divide by 1
		 * but it divides by 8) SDIO IP uses wrong clock and
		 * works unstable (see STAR 9001204800)
		 * We switched to the minimum possible value of the
		 * divisor (div-by-2) in HSDK platform code.
		 * So default mmcclk ciu clock is 50000000 Hz.
		 */
		clock-frequency = <50000000>;
		#clock-cells = <0>;
	};

	mmc: mmc0@f000a000 {
		compatible = "snps,dw-mshc";
		reg = <0xf000a000 0x400>;
		bus-width = <4>;
		fifo-depth = <256>;
		clocks = <&cgu_clk CLK_SYS_SDIO>, <&mmcclk_ciu>;
		clock-names = "biu", "ciu";
		max-frequency = <25000000>;
	};

	spi0: spi@f0020000 {
		compatible = "snps,hsdk-spi", "snps,dw-apb-ssi";
		reg = <0xf0020000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;
		spi-max-frequency = <4000000>;
		clocks = <&cgu_clk CLK_SYS_SPI_REF>;
		clock-names = "spi_clk";
		num-cs = <1>;
		cs-gpios = <&cs_gpio 0>;
		spi_flash@0 {
			compatible = "jedec,spi-nor";
			reg = <0>;
			spi-max-frequency = <4000000>;
		};
	};

	cs_gpio: gpio@f00014b0 {
		compatible = "snps,creg-gpio";
		reg = <0xf00014b0 0x4>;
		gpio-controller;
		#gpio-cells = <1>;
		gpio-bank-name = "hsdk-spi-cs";
		gpio-count = <1>;
		gpio-first-shift = <0>;
		gpio-bit-per-line = <2>;
		gpio-activate-val = <2>;
		gpio-deactivate-val = <3>;
		gpio-default-val = <1>;
	};
};
