/*******************************************************************************
Copyright (C) 2014 - 2019, Marvell International Ltd. and its affiliates
If you received this File from Marvell and you have entered into a commercial
license agreement (a "Commercial License") with Marvell, the File is licensed
to you under the terms of the applicable Commercial License.
*******************************************************************************/

/********************************************************************
This file contains functions for initializing the driver, setting up
the user-provide MDIO access functions, chip reset and various driver
initialization operations for the Marvell X5113 Device.
********************************************************************/
#include "mxdApiTypes.h"
#include "mxdInitialization.h"
#include "mxdFwDownload.h"

MXD_STATUS mxdInitDriver(
    FMXD_READ_MDIO  readMdio,
    FMXD_WRITE_MDIO writeMdio,
    MXD_U16         mdioPort,
    MXD_U16         *pZ80Image,
    MXD_U16         z80Size,
    MXD_U16         *pBusMasterImage,
    MXD_U16         busMasterSize,
    MXD_U16         *pSerdesImage,
    MXD_U16         serdesSize,
    MXD_PVOID       pHostContext,
    MXD_DEV_PTR     pDev
)
{
    if (!pDev || !readMdio || !writeMdio)
        return MXD_FAIL;

    if (mdioPort > MXD_MAX_MDIO_NUM)
        return MXD_FAIL;

    /* Initialize device structure */
    pDev->mdioPort = mdioPort;
    pDev->fmxdReadMdio = readMdio;
    pDev->fmxdWriteMdio = writeMdio;
    pDev->hostContext = pHostContext;
    pDev->devEnabled = MXD_FALSE;
    pDev->devInfo = 0;

    /* Initialize serdes IDs */
    {
        int i, j;
        for (i = 0; i < MXD_NUM_INTERFACE; i++) {
            for (j = 0; j < MXD_NUM_LANES; j++) {
                pDev->serdesID[i][j] = 0;
            }
        }
    }

    /* TODO: Implement actual firmware download and initialization */
    /* For now, just mark as enabled if we have the required firmware */
    if (pBusMasterImage && busMasterSize > 0 && pSerdesImage && serdesSize > 0) {
        /* Download SBus master firmware */
        if (mxdDownloadSBusMasterFW(pDev, mdioPort, pBusMasterImage, busMasterSize) != MXD_OK) {
            return MXD_FAIL;
        }

        /* TODO: Download SerDes firmware */
        /* TODO: Download Z80 firmware if provided */

        pDev->devEnabled = MXD_TRUE;
        return MXD_OK;
    }

    return MXD_FAIL;
}

MXD_STATUS mxdUnloadDriver(
    MXD_DEV_PTR pDev
)
{
    if (!pDev)
        return MXD_FAIL;

    /* TODO: Implement actual cleanup */
    pDev->devEnabled = MXD_FALSE;
    pDev->fmxdReadMdio = NULL;
    pDev->fmxdWriteMdio = NULL;
    pDev->hostContext = NULL;

    return MXD_OK;
}
